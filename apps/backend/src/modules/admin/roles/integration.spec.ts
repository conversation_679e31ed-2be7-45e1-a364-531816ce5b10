import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '../../core/prisma/prisma.service';
import { RolesService } from './roles.service';
import { RoleAssignmentService } from './services/role-assignment.service';
import { RoleHierarchyService } from './services/role-hierarchy.service';
import { UserRoleService } from './services/user-role.service';
import { UnifiedRoleService } from './services/unified-role.service';
import { UserType } from './interfaces/role-assignment.interface';
import { RoleScope } from '@prisma/client';

describe('Role Management Integration Tests', () => {
  let module: TestingModule;
  let prismaService: PrismaService;
  let rolesService: RolesService;
  let roleAssignmentService: RoleAssignmentService;
  let roleHierarchyService: RoleHierarchyService;
  let userRoleService: UserRoleService;
  let unifiedRoleService: UnifiedRoleService;

  // Mock data
  const mockRole = {
    id: 'test-role-1',
    name: 'TEST_ROLE',
    displayName: '測試角色',
    description: '測試用角色',
    scope: RoleScope.SYSTEM,
    isSystem: false,
    tenantId: null,
    parentRoleId: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUser = {
    id: 'test-user-1',
    email: '<EMAIL>',
    name: '測試用戶',
    status: 'ACTIVE',
  };

  const mockPermission = {
    id: 'test-permission-1',
    action: 'read',
    subject: 'User',
    category: 'user-management',
  };

  beforeAll(async () => {
    module = await Test.createTestingModule({
      providers: [
        {
          provide: PrismaService,
          useValue: {
            roles: {
              findUnique: jest.fn(),
              findMany: jest.fn(),
              create: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
            },
            system_users: {
              findUnique: jest.fn(),
              create: jest.fn(),
            },
            tenant_users: {
              findUnique: jest.fn(),
              create: jest.fn(),
            },
            system_user_roles: {
              findMany: jest.fn(),
              create: jest.fn(),
              createMany: jest.fn(),
              deleteMany: jest.fn(),
              count: jest.fn(),
            },
            tenant_user_roles: {
              findMany: jest.fn(),
              create: jest.fn(),
              createMany: jest.fn(),
              deleteMany: jest.fn(),
              count: jest.fn(),
            },
            role_permissions: {
              findMany: jest.fn(),
              create: jest.fn(),
              createMany: jest.fn(),
              deleteMany: jest.fn(),
            },
            permissions: {
              findMany: jest.fn(),
              findUnique: jest.fn(),
            },
            $transaction: jest.fn(),
          },
        },
        RolesService,
        RoleAssignmentService,
        RoleHierarchyService,
        UserRoleService,
        UnifiedRoleService,
      ],
    }).compile();

    prismaService = module.get<PrismaService>(PrismaService);
    rolesService = module.get<RolesService>(RolesService);
    roleAssignmentService = module.get<RoleAssignmentService>(RoleAssignmentService);
    roleHierarchyService = module.get<RoleHierarchyService>(RoleHierarchyService);
    userRoleService = module.get<UserRoleService>(UserRoleService);
    unifiedRoleService = module.get<UnifiedRoleService>(UnifiedRoleService);
  });

  afterAll(async () => {
    await module.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Role Definition and Assignment Integration', () => {
    it('should create role and assign to user successfully', async () => {
      // Arrange
      jest.spyOn(prismaService.roles, 'create').mockResolvedValue(mockRole);
      jest.spyOn(prismaService.roles, 'findUnique').mockResolvedValue({
        ...mockRole,
        role_permissions: [],
        roles: null,
        other_roles: [],
      });
      jest.spyOn(prismaService.system_users, 'findUnique').mockResolvedValue(mockUser);
      jest.spyOn(prismaService.roles, 'findMany').mockResolvedValue([mockRole]);
      jest.spyOn(prismaService.system_user_roles, 'findMany').mockResolvedValue([]);
      jest.spyOn(prismaService.$transaction).mockImplementation(async (callback) => {
        return callback({
          system_user_roles: {
            findMany: jest.fn().mockResolvedValue([]),
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
        });
      });

      // Act
      const createdRole = await rolesService.create({
        name: 'TEST_ROLE',
        displayName: '測試角色',
        description: '測試用角色',
        scope: RoleScope.SYSTEM,
        isSystem: false,
        permissions: [],
      });

      const assignmentResult = await roleAssignmentService.assignRoles({
        userId: 'test-user-1',
        userType: UserType.SYSTEM,
        roleIds: [createdRole.id],
        reason: '測試指派',
        assignedBy: 'admin',
      });

      // Assert
      expect(createdRole.id).toBe('test-role-1');
      expect(assignmentResult.success).toBe(true);
      expect(assignmentResult.assignedRoles).toContain('test-role-1');
    });

    it('should handle role hierarchy correctly', async () => {
      // Arrange
      const parentRole = { ...mockRole, id: 'parent-role' };
      const childRole = { ...mockRole, id: 'child-role', parentRoleId: 'parent-role' };

      jest.spyOn(prismaService.roles, 'findUnique')
        .mockImplementation(async ({ where }) => {
          if (where.id === 'parent-role') {
            return {
              ...parentRole,
              role_permissions: [{ permissionId: 'test-permission-1' }],
              roles: null,
              other_roles: [childRole],
            };
          }
          if (where.id === 'child-role') {
            return {
              ...childRole,
              role_permissions: [],
              roles: parentRole,
              other_roles: [],
            };
          }
          return null;
        });

      jest.spyOn(prismaService.role_permissions, 'findMany')
        .mockResolvedValue([{ roleId: 'parent-role', permissionId: 'test-permission-1' }]);

      // Act
      const hierarchy = await roleHierarchyService.getRoleHierarchy('child-role');
      const effectivePermissions = await roleHierarchyService.getEffectivePermissions('child-role');

      // Assert
      expect(hierarchy.parentRoleId).toBe('parent-role');
      expect(hierarchy.level).toBe(1);
      expect(effectivePermissions).toContain('test-permission-1');
    });
  });

  describe('Unified Service Integration', () => {
    it('should provide complete role information', async () => {
      // Arrange
      jest.spyOn(rolesService, 'findOne').mockResolvedValue({
        ...mockRole,
        permissions: ['test-permission-1'],
      });

      jest.spyOn(roleHierarchyService, 'getRoleHierarchy').mockResolvedValue({
        roleId: 'test-role-1',
        roleName: 'TEST_ROLE',
        parentRoleId: undefined,
        childRoleIds: [],
        level: 0,
        inheritedPermissions: [],
        directPermissions: ['test-permission-1'],
      });

      jest.spyOn(userRoleService, 'getUsersByRole').mockResolvedValue([
        {
          userId: 'test-user-1',
          userType: UserType.SYSTEM,
          user: mockUser,
          roles: [{
            id: 'test-role-1',
            name: 'TEST_ROLE',
            displayName: '測試角色',
            description: '測試用角色',
            scope: RoleScope.SYSTEM,
            isSystem: false,
            assignedAt: new Date(),
          }],
        },
      ]);

      jest.spyOn(rolesService, 'getRoleUserStatistics').mockResolvedValue({
        totalUsers: 1,
        systemUsers: 1,
        tenantUsers: 0,
      });

      jest.spyOn(roleHierarchyService, 'getEffectivePermissions')
        .mockResolvedValue(['test-permission-1']);

      // Act
      const completeInfo = await unifiedRoleService.getCompleteRoleInfo('test-role-1');

      // Assert
      expect(completeInfo.role.id).toBe('test-role-1');
      expect(completeInfo.users.total).toHaveLength(1);
      expect(completeInfo.permissions.effective).toContain('test-permission-1');
      expect(completeInfo.statistics.userCount).toBe(1);
      expect(completeInfo.statistics.effectivePermissions).toBe(1);
    });

    it('should perform role health check', async () => {
      // Arrange
      jest.spyOn(rolesService, 'findOne').mockResolvedValue({
        ...mockRole,
        permissions: ['test-permission-1'],
      });

      jest.spyOn(roleHierarchyService, 'getRoleHierarchy').mockResolvedValue({
        roleId: 'test-role-1',
        roleName: 'TEST_ROLE',
        parentRoleId: undefined,
        childRoleIds: [],
        level: 0,
        inheritedPermissions: [],
        directPermissions: ['test-permission-1'],
      });

      jest.spyOn(roleHierarchyService, 'getAllParentRoles').mockResolvedValue([]);
      jest.spyOn(userRoleService, 'getUsersByRole').mockResolvedValue([]);

      // Act
      const healthCheck = await unifiedRoleService.performRoleHealthCheck('test-role-1');

      // Assert
      expect(healthCheck.isHealthy).toBe(true);
      expect(healthCheck.warnings).toContain('角色沒有指派給任何用戶');
      expect(healthCheck.warnings).toContain('角色沒有父子關係，可能是孤立角色');
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle role not found gracefully', async () => {
      // Arrange
      jest.spyOn(prismaService.roles, 'findUnique').mockResolvedValue(null);

      // Act & Assert
      await expect(rolesService.findOne('non-existent-role')).rejects.toThrow('找不到 ID 為 non-existent-role 的角色');
    });

    it('should handle assignment to non-existent user', async () => {
      // Arrange
      jest.spyOn(prismaService.system_users, 'findUnique').mockResolvedValue(null);

      // Act
      const result = await roleAssignmentService.assignRoles({
        userId: 'non-existent-user',
        userType: UserType.SYSTEM,
        roleIds: ['test-role-1'],
      });

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('系統用戶 non-existent-user 不存在');
    });
  });

  describe('Performance Integration', () => {
    it('should handle batch operations efficiently', async () => {
      // Arrange
      const operations = Array.from({ length: 10 }, (_, i) => ({
        userId: `user-${i}`,
        userType: UserType.SYSTEM,
        action: 'assign' as const,
        roleIds: ['test-role-1'],
      }));

      jest.spyOn(prismaService.system_users, 'findUnique').mockResolvedValue(mockUser);
      jest.spyOn(prismaService.roles, 'findMany').mockResolvedValue([mockRole]);
      jest.spyOn(prismaService.system_user_roles, 'findMany').mockResolvedValue([]);
      jest.spyOn(prismaService.$transaction).mockImplementation(async (callback) => {
        return callback({
          system_user_roles: {
            findMany: jest.fn().mockResolvedValue([]),
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
        });
      });

      // Act
      const startTime = Date.now();
      const result = await roleAssignmentService.batchRoleOperations({
        operations,
        reason: '批量測試',
        operatedBy: 'admin',
      });
      const executionTime = Date.now() - startTime;

      // Assert
      expect(result.successCount).toBe(10);
      expect(result.failureCount).toBe(0);
      expect(executionTime).toBeLessThan(5000); // 應該在 5 秒內完成
    });
  });
});
