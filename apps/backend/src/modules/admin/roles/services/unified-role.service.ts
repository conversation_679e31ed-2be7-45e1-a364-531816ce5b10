import {
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { RolesService } from '../roles.service';
import { RoleAssignmentService } from './role-assignment.service';
import { RoleHierarchyService } from './role-hierarchy.service';
import { UserRoleService } from './user-role.service';
import { UserType } from '../types/role.types';

/**
 * 統一角色管理服務
 * 整合所有角色管理功能，提供統一的介面
 */
@Injectable()
export class UnifiedRoleService {
  private readonly logger = new Logger(UnifiedRoleService.name);

  constructor(
    private readonly rolesService: RolesService,
    private readonly roleAssignmentService: RoleAssignmentService,
    private readonly roleHierarchyService: RoleHierarchyService,
    private readonly userRoleService: UserRoleService
  ) {}

  /**
   * 獲取角色的完整資訊
   * 包含角色定義、層級關係、用戶資訊和統計資料
   */
  async getCompleteRoleInfo(roleId: string) {
    this.logger.debug(`Getting complete role info for role: ${roleId}`);

    try {
      // 並行獲取所有相關資訊
      const [
        roleDetail,
        hierarchy,
        users,
        userStatistics,
        effectivePermissions,
      ] = await Promise.all([
        this.rolesService.findOne(roleId, true), // 包含層級資訊
        this.roleHierarchyService.getRoleHierarchy(roleId),
        this.userRoleService.getUsersByRole(roleId),
        this.rolesService.getRoleUserStatistics(roleId),
        this.roleHierarchyService.getEffectivePermissions(roleId),
      ]);

      // 計算詳細統計
      const statistics = {
        userCount: users.length,
        systemUsers: users.filter(u => u.userType === UserType.SYSTEM).length,
        tenantUsers: users.filter(u => u.userType === UserType.TENANT).length,
        directPermissions: hierarchy.directPermissions.length,
        inheritedPermissions: hierarchy.inheritedPermissions.length,
        effectivePermissions: effectivePermissions.length,
        hierarchyLevel: hierarchy.level,
        childRolesCount: hierarchy.childRoleIds.length,
        hasParentRole: !!hierarchy.parentRoleId,
      };

      // 按租戶分組用戶
      const usersByTenant = users
        .filter(u => u.userType === UserType.TENANT && u.tenant)
        .reduce((acc, user) => {
          const tenantId = user.tenant!.id;
          if (!acc[tenantId]) {
            acc[tenantId] = {
              tenantId,
              tenantName: user.tenant!.name,
              users: [],
            };
          }
          acc[tenantId].users.push(user);
          return acc;
        }, {} as Record<string, any>);

      const result = {
        role: roleDetail,
        hierarchy,
        users: {
          total: users,
          systemUsers: users.filter(u => u.userType === UserType.SYSTEM),
          tenantUsers: users.filter(u => u.userType === UserType.TENANT),
          byTenant: Object.values(usersByTenant),
        },
        permissions: {
          direct: hierarchy.directPermissions,
          inherited: hierarchy.inheritedPermissions,
          effective: effectivePermissions,
        },
        statistics,
        metadata: {
          lastUpdated: new Date(),
          dataVersion: '1.0',
        },
      };

      this.logger.debug(
        `Complete role info retrieved for ${roleId}: ${users.length} users, ${effectivePermissions.length} permissions`
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to get complete role info for ${roleId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * 獲取角色摘要資訊
   * 輕量級的角色資訊，適用於列表顯示
   */
  async getRoleSummary(roleId: string) {
    this.logger.debug(`Getting role summary for: ${roleId}`);

    try {
      const [roleDetail, userCount, hierarchy] = await Promise.all([
        this.rolesService.findOne(roleId),
        this.rolesService.getUserCount(roleId),
        this.roleHierarchyService.getRoleHierarchy(roleId).catch(() => null), // 容錯處理
      ]);

      return {
        id: roleDetail.id,
        name: roleDetail.name,
        displayName: roleDetail.displayName,
        description: roleDetail.description,
        scope: roleDetail.scope,
        isSystem: roleDetail.isSystem,
        userCount,
        permissionCount: roleDetail.permissions?.length || 0,
        hierarchyLevel: hierarchy?.level || 0,
        hasChildren: (hierarchy?.childRoleIds?.length || 0) > 0,
        hasParent: !!hierarchy?.parentRoleId,
        createdAt: roleDetail.createdAt,
        updatedAt: roleDetail.updatedAt,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get role summary for ${roleId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * 批量獲取角色摘要
   */
  async getBatchRoleSummaries(roleIds: string[]) {
    this.logger.debug(`Getting batch role summaries for ${roleIds.length} roles`);

    const summaries = await Promise.allSettled(
      roleIds.map(roleId => this.getRoleSummary(roleId))
    );

    const successful = summaries
      .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
      .map(result => result.value);

    const failed = summaries
      .map((result, index) => ({ result, roleId: roleIds[index] }))
      .filter(({ result }) => result.status === 'rejected')
      .map(({ result, roleId }) => ({
        roleId,
        error: (result as PromiseRejectedResult).reason.message,
      }));

    if (failed.length > 0) {
      this.logger.warn(`Failed to get summaries for ${failed.length} roles:`, failed);
    }

    return {
      successful,
      failed,
      totalRequested: roleIds.length,
      successCount: successful.length,
      failureCount: failed.length,
    };
  }

  /**
   * 角色健康檢查
   * 檢查角色的完整性和一致性
   */
  async performRoleHealthCheck(roleId: string) {
    this.logger.debug(`Performing health check for role: ${roleId}`);

    const healthCheck = {
      roleId,
      isHealthy: true,
      issues: [] as string[],
      warnings: [] as string[],
      recommendations: [] as string[],
      checkedAt: new Date(),
    };

    try {
      // 檢查角色是否存在
      const role = await this.rolesService.findOne(roleId);
      if (!role) {
        healthCheck.isHealthy = false;
        healthCheck.issues.push('角色不存在');
        return healthCheck;
      }

      // 檢查層級結構
      try {
        const hierarchy = await this.roleHierarchyService.getRoleHierarchy(roleId);
        
        // 檢查循環引用
        const allParents = await this.roleHierarchyService.getAllParentRoles(roleId);
        if (allParents.includes(roleId)) {
          healthCheck.isHealthy = false;
          healthCheck.issues.push('檢測到循環引用');
        }

        // 檢查孤立角色
        if (!hierarchy.parentRoleId && hierarchy.childRoleIds.length === 0) {
          healthCheck.warnings.push('角色沒有父子關係，可能是孤立角色');
        }

        // 檢查權限繼承
        if (hierarchy.inheritedPermissions.length === 0 && hierarchy.parentRoleId) {
          healthCheck.warnings.push('有父角色但沒有繼承權限');
        }
      } catch (error) {
        healthCheck.warnings.push(`層級檢查失敗: ${error.message}`);
      }

      // 檢查用戶指派
      try {
        const users = await this.userRoleService.getUsersByRole(roleId);
        if (users.length === 0) {
          healthCheck.warnings.push('角色沒有指派給任何用戶');
        }

        // 檢查租戶一致性
        const tenantUsers = users.filter(u => u.userType === UserType.TENANT);
        if (tenantUsers.length > 0 && role.scope === 'SYSTEM') {
          healthCheck.issues.push('系統角色被指派給租戶用戶');
          healthCheck.isHealthy = false;
        }
      } catch (error) {
        healthCheck.warnings.push(`用戶檢查失敗: ${error.message}`);
      }

      // 檢查權限配置
      if (!role.permissions || role.permissions.length === 0) {
        healthCheck.warnings.push('角色沒有配置任何權限');
        healthCheck.recommendations.push('考慮為角色配置適當的權限');
      }

      // 生成建議
      if (healthCheck.warnings.length > 0) {
        healthCheck.recommendations.push('檢查並解決警告項目');
      }

      if (role.scope === 'TENANT' && !role.tenantId) {
        healthCheck.warnings.push('租戶角色沒有指定租戶 ID');
      }

    } catch (error) {
      healthCheck.isHealthy = false;
      healthCheck.issues.push(`健康檢查失敗: ${error.message}`);
    }

    this.logger.debug(
      `Health check completed for ${roleId}: ${healthCheck.isHealthy ? 'healthy' : 'unhealthy'} ` +
      `(${healthCheck.issues.length} issues, ${healthCheck.warnings.length} warnings)`
    );

    return healthCheck;
  }

  /**
   * 角色使用分析
   * 分析角色的使用情況和影響範圍
   */
  async analyzeRoleUsage(roleId: string) {
    this.logger.debug(`Analyzing role usage for: ${roleId}`);

    try {
      const [
        completeInfo,
        childRoles,
        parentRoles,
      ] = await Promise.all([
        this.getCompleteRoleInfo(roleId),
        this.roleHierarchyService.getAllChildRoles(roleId),
        this.roleHierarchyService.getAllParentRoles(roleId),
      ]);

      // 計算影響範圍
      const impactScope = {
        directUsers: completeInfo.users.total.length,
        indirectUsers: 0, // 通過子角色影響的用戶
        affectedRoles: childRoles.length + parentRoles.length,
        permissionScope: completeInfo.permissions.effective.length,
      };

      // 計算間接影響的用戶
      if (childRoles.length > 0) {
        const childRoleUsers = await Promise.all(
          childRoles.map(childRoleId => 
            this.userRoleService.getUsersByRole(childRoleId)
          )
        );
        impactScope.indirectUsers = childRoleUsers.flat().length;
      }

      // 使用模式分析
      const usagePatterns = {
        isActivelyUsed: completeInfo.users.total.length > 0,
        isHierarchyRoot: parentRoles.length === 0 && childRoles.length > 0,
        isHierarchyLeaf: childRoles.length === 0 && parentRoles.length > 0,
        isIsolated: parentRoles.length === 0 && childRoles.length === 0,
        hasSystemUsers: completeInfo.users.systemUsers.length > 0,
        hasTenantUsers: completeInfo.users.tenantUsers.length > 0,
        isMultiTenant: Object.keys(completeInfo.users.byTenant).length > 1,
      };

      // 風險評估
      const riskAssessment = {
        deletionRisk: (impactScope.directUsers > 0 ? 'high' : 'low') as 'low' | 'medium' | 'high',
        modificationRisk: (impactScope.indirectUsers > 0 ? 'medium' : 'low') as 'low' | 'medium' | 'high',
        securityRisk: (completeInfo.permissions.effective.includes('manage:all') ? 'high' : 'low') as 'low' | 'medium' | 'high',
      };

      return {
        roleId,
        roleName: completeInfo.role.name,
        impactScope,
        usagePatterns,
        riskAssessment,
        hierarchy: {
          level: completeInfo.hierarchy.level,
          parentRoles,
          childRoles,
        },
        recommendations: this.generateUsageRecommendations(usagePatterns, impactScope),
        analyzedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to analyze role usage for ${roleId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * 生成使用建議
   */
  private generateUsageRecommendations(
    patterns: any,
    impact: any
  ): string[] {
    const recommendations: string[] = [];

    if (!patterns.isActivelyUsed) {
      recommendations.push('角色未被使用，考慮刪除或重新設計');
    }

    if (patterns.isIsolated && patterns.isActivelyUsed) {
      recommendations.push('考慮將角色整合到層級結構中');
    }

    if (impact.directUsers > 50) {
      recommendations.push('用戶數量較多，變更時需謹慎');
    }

    if (patterns.isMultiTenant) {
      recommendations.push('跨多個租戶使用，確保權限隔離');
    }

    return recommendations;
  }
}
