import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  Query,
  Req,
  ForbiddenException,
  NotFoundException,
} from "@nestjs/common";
import { RolesService } from "./roles.service";
import {
  CreateRoleDto,
  UpdateRoleDto,
  UpdateRolePermissionsDto,
  RoleResponseDto,
  RoleWithPermissionsResponseDto,
  RoleUserCountResponseDto,
  RoleScope,
  PermissionDto,
  PermissionCategoryDto,
} from "./dto/role.dto";
import { JwtAuthGuard } from "../../core/auth/guards/auth.guard";
import { PoliciesGuard } from "../../../casl/guards/permission.guard";
import { CheckPolicies } from "../../../casl/decorators/check-policies.decorator";
import { AppAbility } from "../../../types/models/casl.model";
import { Actions, Subjects } from "@horizai/permissions";
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { Request } from "express";

// Define constants for role names used in @Roles decorator, these should match DB entries for system roles
const SYSTEM_ADMIN_ROLE = "SystemAdmin"; // Example, adjust to actual name in DB
const SUPER_ADMIN_ROLE = "SuperAdmin"; // Example, adjust to actual name in DB
const TENANT_ADMIN_ROLE = "TenantAdmin"; // Example, adjust to actual name in DB

@ApiTags("admin/roles")
@ApiBearerAuth()
@Controller("admin/roles")
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Get("permissions")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.PERMISSION)
  )
  @ApiOperation({
    summary: "讀取所有權限",
    description: "取得系統中所有權限的列表，用於權限矩陣顯示",
  })
  @ApiResponse({
    status: 200,
    description: "成功讀取權限列表",
    type: [PermissionDto],
  })
  async getAllPermissions(): Promise<PermissionDto[]> {
    return this.rolesService.getAllPermissions();
  }

  @Get("permissions/categories")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.PERMISSION)
  )
  @ApiOperation({
    summary: "讀取所有權限分類",
    description: "取得系統中所有權限分類的列表，用於權限矩陣分組顯示",
  })
  @ApiResponse({
    status: 200,
    description: "成功讀取權限分類列表",
    type: [PermissionCategoryDto],
  })
  async getAllPermissionCategories(): Promise<PermissionCategoryDto[]> {
    return this.rolesService.getAllPermissionCategories();
  }

  @Get()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "讀取所有角色",
    description: "取得系統中所有角色的列表，可依 scope 篩選",
  })
  @ApiQuery({
    name: "scope",
    required: false,
    enum: RoleScope,
    description: "角色區域過濾 (SYSTEM, TENANT, WORKSPACE)",
  })
  @ApiResponse({
    status: 200,
    description: "成功讀取角色列表",
    type: [RoleResponseDto],
  })
  async findAll(@Query("scope") scope?: RoleScope): Promise<RoleResponseDto[]> {
    const roles = await this.rolesService.findAll(scope);
    return roles.map((role) => ({
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description ?? undefined,
      isSystem: role.isSystem,
      scope: role.scope as RoleScope,
      tenantId: role.tenantId,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    }));
  }

  @Get(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "讀取單個角色詳情",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({
    status: 200,
    description: "成功讀取角色詳情",
    type: RoleWithPermissionsResponseDto,
  })
  async findOne(
    @Param("id") id: string,
    @Req() req: Request
  ): Promise<RoleWithPermissionsResponseDto> {
    const roleDetail = await this.rolesService.findOne(id);

    const ability = req.ability;
    if (!ability || !ability.can(Actions.READ, roleDetail as any)) {
      throw new ForbiddenException("您沒有權限讀取此角色的詳細資訊。");
    }
    return {
      id: roleDetail.id,
      name: roleDetail.name,
      displayName: roleDetail.displayName,
      description: roleDetail.description ?? undefined,
      isSystem: roleDetail.isSystem,
      scope: roleDetail.scope as RoleScope,
      tenantId: roleDetail.tenantId,
      createdAt: roleDetail.createdAt,
      updatedAt: roleDetail.updatedAt,
      permissions: roleDetail.permissions,
    };
  }

  @Post()
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.CREATE, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "建立新角色",
  })
  @ApiResponse({
    status: 201,
    description: "角色建立成功",
    type: RoleResponseDto,
  })
  async create(@Body() createRoleDto: CreateRoleDto): Promise<RoleResponseDto> {
    const role = await this.rolesService.create(createRoleDto);
    return {
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description ?? undefined,
      isSystem: role.isSystem,
      scope: role.scope as RoleScope,
      tenantId: role.tenantId,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    };
  }

  @Put(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "更新角色",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({
    status: 200,
    description: "角色更新成功",
    type: RoleResponseDto,
  })
  async update(
    @Param("id") id: string,
    @Body() updateRoleDto: UpdateRoleDto,
    @Req() req: Request
  ): Promise<RoleResponseDto> {
    const ability = req.ability;
    const roleToUpdate = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.UPDATE, roleToUpdate as any)) {
      throw new ForbiddenException("您沒有權限更新此角色。");
    }

    const role = await this.rolesService.update(id, updateRoleDto);
    return {
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description ?? undefined,
      isSystem: role.isSystem,
      scope: role.scope as RoleScope,
      tenantId: role.tenantId,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    };
  }

  @Delete(":id")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.DELETE, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "刪除角色",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({ status: 200, description: "角色刪除成功" })
  async remove(
    @Param("id") id: string,
    @Req() req: Request
  ): Promise<{ success: boolean; message: string }> {
    const ability = req.ability;
    const roleToDelete = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.DELETE, roleToDelete as any)) {
      throw new ForbiddenException("您沒有權限刪除此角色。");
    }
    return this.rolesService.remove(id);
  }

  @Put(":id/permissions")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.UPDATE, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "更新角色權限",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({
    status: 200,
    description: "角色權限更新成功",
  })
  async updateRolePermissions(
    @Param("id") id: string,
    @Body() updatePermissionsDto: UpdateRolePermissionsDto,
    @Req() req: Request
  ): Promise<{ success: boolean; message: string }> {
    const ability = req.ability;
    const roleToUpdatePermissions = await this.rolesService.findOne(id);

    if (
      !ability ||
      !ability.can(Actions.UPDATE, roleToUpdatePermissions as any)
    ) {
      throw new ForbiddenException("您沒有權限更新此角色的權限。");
    }
    return this.rolesService.updateRolePermissions(
      id,
      updatePermissionsDto.permissions
    );
  }

  @Get(":id/users/count")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "讀取角色使用者數量",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({
    status: 200,
    description: "成功讀取角色使用者數量",
    type: RoleUserCountResponseDto,
  })
  async getUserCount(
    @Param("id") id: string,
    @Req() req: Request
  ): Promise<RoleUserCountResponseDto> {
    const ability = req.ability;
    const roleToCountUsers = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, roleToCountUsers as any)) {
      throw new ForbiddenException("您沒有權限讀取此角色的使用者數量。");
    }
    const count = await this.rolesService.getUserCount(id);
    return { count };
  }

  @Get(":id/users")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "獲取角色的詳細用戶資訊",
    description: "獲取指定角色的所有用戶詳細資訊，包含系統用戶和租戶用戶",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({
    status: 200,
    description: "成功獲取角色用戶資訊",
  })
  async getRoleUsers(
    @Param("id") id: string,
    @Req() req: Request
  ) {
    const ability = req.ability;
    const role = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException("您沒有權限讀取此角色的用戶資訊。");
    }

    try {
      // 動態導入服務以避免循環依賴
      const { UserRoleService } = await import('./services/user-role.service');
      const userRoleService = new UserRoleService(
        this.rolesService['prisma'], // 訪問 prisma 實例
        this.rolesService as any,
        null as any
      );

      return userRoleService.getUsersByRole(id);
    } catch (error) {
      throw new NotFoundException(`無法獲取角色 ${id} 的用戶資訊: ${error.message}`);
    }
  }

  @Get(":id/effective-permissions")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "獲取角色的有效權限",
    description: "獲取角色的有效權限，包含直接權限和繼承權限",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({
    status: 200,
    description: "成功獲取角色有效權限",
    type: [String],
  })
  async getEffectivePermissions(
    @Param("id") id: string,
    @Req() req: Request
  ): Promise<string[]> {
    const ability = req.ability;
    const role = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException("您沒有權限讀取此角色的權限資訊。");
    }

    try {
      // 動態導入服務以避免循環依賴
      const { RoleHierarchyService } = await import('./services/role-hierarchy.service');
      const roleHierarchyService = new RoleHierarchyService(
        this.rolesService['prisma'] // 訪問 prisma 實例
      );

      return roleHierarchyService.getEffectivePermissions(id);
    } catch (error) {
      throw new NotFoundException(`無法獲取角色 ${id} 的有效權限: ${error.message}`);
    }
  }

  @Get(":id/hierarchy")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "獲取角色層級資訊",
    description: "獲取角色的完整層級資訊，包含父子關係和權限繼承",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({
    status: 200,
    description: "成功獲取角色層級資訊",
  })
  async getRoleHierarchy(
    @Param("id") id: string,
    @Req() req: Request
  ) {
    const ability = req.ability;
    const role = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException("您沒有權限讀取此角色的層級資訊。");
    }

    try {
      // 動態導入服務以避免循環依賴
      const { RoleHierarchyService } = await import('./services/role-hierarchy.service');
      const roleHierarchyService = new RoleHierarchyService(
        this.rolesService['prisma'] // 訪問 prisma 實例
      );

      return roleHierarchyService.getRoleHierarchy(id);
    } catch (error) {
      throw new NotFoundException(`無法獲取角色 ${id} 的層級資訊: ${error.message}`);
    }
  }

  @Get(":id/statistics")
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.READ, Subjects.ROLE)
  )
  @ApiOperation({
    summary: "獲取角色詳細統計資訊",
    description: "獲取角色的詳細統計資訊，包含用戶分布和權限統計",
  })
  @ApiParam({ name: "id", description: "角色ID" })
  @ApiResponse({
    status: 200,
    description: "成功獲取角色統計資訊",
  })
  async getRoleStatistics(
    @Param("id") id: string,
    @Req() req: Request
  ) {
    const ability = req.ability;
    const role = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException("您沒有權限讀取此角色的統計資訊。");
    }

    const statistics = await this.rolesService.getRoleUserStatistics(id);
    return statistics;
  }
}
