import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { RoleHierarchyInfo } from '../types/role.types';
import { RoleScope } from '@prisma/client';

/**
 * 角色層級管理服務
 * 負責管理角色的層級關係和權限繼承
 */
@Injectable()
export class RoleHierarchyService {
  private readonly logger = new Logger(RoleHierarchyService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 獲取角色層級資訊
   */
  async getRoleHierarchy(roleId: string): Promise<RoleHierarchyInfo> {
    const role = await this.prisma.roles.findUnique({
      where: { id: roleId },
      include: {
        roles: true, // 父角色
        other_roles: true, // 子角色
        role_permissions: {
          include: {
            permissions: true,
          },
        },
      },
    });

    if (!role) {
      throw new NotFoundException(`角色 ${roleId} 不存在`);
    }

    // 獲取繼承的權限
    const inheritedPermissions = await this.getInheritedPermissions(roleId);
    const directPermissions = role.role_permissions.map((rp) => rp.permissions.id);

    // 計算層級深度
    const level = await this.calculateRoleLevel(roleId);

    return {
      roleId: role.id,
      roleName: role.name,
      parentRoleId: role.parentRoleId || undefined,
      childRoleIds: role.other_roles.map((r) => r.id),
      level,
      inheritedPermissions,
      directPermissions,
    };
  }

  /**
   * 設置角色的父角色
   */
  async setParentRole(roleId: string, parentRoleId: string | null): Promise<void> {
    this.logger.debug(`Setting parent role for ${roleId}: ${parentRoleId}`);

    // 驗證角色存在
    const role = await this.prisma.roles.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException(`角色 ${roleId} 不存在`);
    }

    if (parentRoleId) {
      const parentRole = await this.prisma.roles.findUnique({
        where: { id: parentRoleId },
      });

      if (!parentRole) {
        throw new NotFoundException(`父角色 ${parentRoleId} 不存在`);
      }

      // 檢查是否會造成循環引用
      if (await this.wouldCreateCycle(roleId, parentRoleId)) {
        throw new ConflictException('設置父角色會造成循環引用');
      }

      // 檢查範圍兼容性
      if (!this.areScopesCompatible(role.scope, parentRole.scope)) {
        throw new BadRequestException(
          `角色範圍不兼容: ${role.scope} 不能繼承自 ${parentRole.scope}`
        );
      }

      // 檢查租戶兼容性
      if (role.tenantId !== parentRole.tenantId) {
        throw new BadRequestException('角色和父角色必須屬於同一租戶');
      }
    }

    // 更新父角色
    await this.prisma.roles.update({
      where: { id: roleId },
      data: { parentRoleId },
    });

    this.logger.log(`Successfully set parent role for ${roleId}: ${parentRoleId}`);
  }

  /**
   * 獲取角色的所有子角色（遞歸）
   */
  async getAllChildRoles(roleId: string): Promise<string[]> {
    const childRoles: string[] = [];
    const visited = new Set<string>();

    const collectChildren = async (currentRoleId: string) => {
      if (visited.has(currentRoleId)) {
        return; // 避免無限循環
      }
      visited.add(currentRoleId);

      const directChildren = await this.prisma.roles.findMany({
        where: { parentRoleId: currentRoleId },
        select: { id: true },
      });

      for (const child of directChildren) {
        childRoles.push(child.id);
        await collectChildren(child.id);
      }
    };

    await collectChildren(roleId);
    return childRoles;
  }

  /**
   * 獲取角色的所有父角色（遞歸）
   */
  async getAllParentRoles(roleId: string): Promise<string[]> {
    const parentRoles: string[] = [];
    const visited = new Set<string>();

    const collectParents = async (currentRoleId: string) => {
      if (visited.has(currentRoleId)) {
        return; // 避免無限循環
      }
      visited.add(currentRoleId);

      const role = await this.prisma.roles.findUnique({
        where: { id: currentRoleId },
        select: { parentRoleId: true },
      });

      if (role?.parentRoleId) {
        parentRoles.push(role.parentRoleId);
        await collectParents(role.parentRoleId);
      }
    };

    await collectParents(roleId);
    return parentRoles;
  }

  /**
   * 獲取繼承的權限
   */
  async getInheritedPermissions(roleId: string): Promise<string[]> {
    const parentRoles = await this.getAllParentRoles(roleId);
    
    if (parentRoles.length === 0) {
      return [];
    }

    const inheritedPermissions = await this.prisma.role_permissions.findMany({
      where: {
        roleId: { in: parentRoles },
      },
      select: { permissionId: true },
    });

    return [...new Set(inheritedPermissions.map((rp) => rp.permissionId))];
  }

  /**
   * 獲取角色的有效權限（直接權限 + 繼承權限）
   */
  async getEffectivePermissions(roleId: string): Promise<string[]> {
    const [directPermissions, inheritedPermissions] = await Promise.all([
      this.getDirectPermissions(roleId),
      this.getInheritedPermissions(roleId),
    ]);

    return [...new Set([...directPermissions, ...inheritedPermissions])];
  }

  /**
   * 驗證角色層級結構
   */
  async validateHierarchy(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 檢查循環引用
    const cycles = await this.detectCycles();
    if (cycles.length > 0) {
      errors.push(`檢測到循環引用: ${cycles.join(', ')}`);
    }

    // 檢查孤立角色
    const orphanedRoles = await this.findOrphanedRoles();
    if (orphanedRoles.length > 0) {
      warnings.push(`發現孤立角色: ${orphanedRoles.join(', ')}`);
    }

    // 檢查範圍不兼容
    const scopeConflicts = await this.findScopeConflicts();
    if (scopeConflicts.length > 0) {
      errors.push(`範圍衝突: ${scopeConflicts.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 計算角色層級深度
   */
  private async calculateRoleLevel(roleId: string): Promise<number> {
    const parentRoles = await this.getAllParentRoles(roleId);
    return parentRoles.length;
  }

  /**
   * 檢查是否會造成循環引用
   */
  private async wouldCreateCycle(roleId: string, parentRoleId: string): Promise<boolean> {
    const childRoles = await this.getAllChildRoles(roleId);
    return childRoles.includes(parentRoleId);
  }

  /**
   * 檢查範圍兼容性
   */
  private areScopesCompatible(childScope: RoleScope, parentScope: RoleScope): boolean {
    // 系統角色只能繼承自系統角色
    if (childScope === RoleScope.SYSTEM) {
      return parentScope === RoleScope.SYSTEM;
    }

    // 租戶角色可以繼承自系統角色或租戶角色
    if (childScope === RoleScope.TENANT) {
      return parentScope === RoleScope.SYSTEM || parentScope === RoleScope.TENANT;
    }

    // 工作區角色可以繼承自任何角色
    if (childScope === RoleScope.WORKSPACE) {
      return true;
    }

    return false;
  }

  /**
   * 獲取直接權限
   */
  private async getDirectPermissions(roleId: string): Promise<string[]> {
    const rolePermissions = await this.prisma.role_permissions.findMany({
      where: { roleId },
      select: { permissionId: true },
    });

    return rolePermissions.map((rp) => rp.permissionId);
  }

  /**
   * 檢測循環引用
   */
  private async detectCycles(): Promise<string[]> {
    const cycles: string[] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const roles = await this.prisma.roles.findMany({
      select: { id: true, parentRoleId: true },
    });

    const hasCycle = (roleId: string): boolean => {
      if (recursionStack.has(roleId)) {
        cycles.push(roleId);
        return true;
      }

      if (visited.has(roleId)) {
        return false;
      }

      visited.add(roleId);
      recursionStack.add(roleId);

      const role = roles.find((r) => r.id === roleId);
      if (role?.parentRoleId && hasCycle(role.parentRoleId)) {
        return true;
      }

      recursionStack.delete(roleId);
      return false;
    };

    for (const role of roles) {
      if (!visited.has(role.id)) {
        hasCycle(role.id);
      }
    }

    return cycles;
  }

  /**
   * 查找孤立角色
   */
  private async findOrphanedRoles(): Promise<string[]> {
    const rolesWithoutParentOrChildren = await this.prisma.roles.findMany({
      where: {
        AND: [
          { parentRoleId: null },
          {
            other_roles: {
              none: {},
            },
          },
        ],
      },
      select: { id: true },
    });

    return rolesWithoutParentOrChildren.map((r) => r.id);
  }

  /**
   * 查找範圍衝突
   */
  private async findScopeConflicts(): Promise<string[]> {
    const conflicts: string[] = [];

    const rolesWithParents = await this.prisma.roles.findMany({
      where: { parentRoleId: { not: null } },
      include: { roles: true },
    });

    for (const role of rolesWithParents) {
      if (role.roles && !this.areScopesCompatible(role.scope, role.roles.scope)) {
        conflicts.push(`${role.id} (${role.scope}) -> ${role.roles.id} (${role.roles.scope})`);
      }
    }

    return conflicts;
  }
}
