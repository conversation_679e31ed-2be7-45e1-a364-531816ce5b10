import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { UnifiedRoleService } from '../services/unified-role.service';
import { UnifiedPermissionGuard } from '../../../../casl/guards/unified-permission.guard';
import { RequireRead, RequireManage } from '../../../../casl/decorators/require-permissions.decorator';
import {
  CompleteRoleInfo,
  RoleSummary,
  RoleHealthCheck,
  RoleUsageAnalysis,
  BatchRoleSummaryResult,
} from '../types/role.types';

/**
 * 統一角色管理控制器
 * 提供整合的角色管理功能和高級分析
 */
@ApiTags('admin/unified-roles')
@ApiBearerAuth()
@Controller('admin/unified-roles')
@UseGuards(UnifiedPermissionGuard)
export class UnifiedRoleController {
  constructor(private readonly unifiedRoleService: UnifiedRoleService) {}

  /**
   * 獲取角色完整資訊
   */
  @Get(':id/complete')
  @RequireRead('Role')
  @ApiOperation({
    summary: '獲取角色完整資訊',
    description: '獲取角色的完整資訊，包含定義、層級、用戶、權限和統計資料',
  })
  @ApiParam({ name: 'id', description: '角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色完整資訊',
    type: Object,
  })
  async getCompleteRoleInfo(@Param('id') id: string): Promise<CompleteRoleInfo> {
    return this.unifiedRoleService.getCompleteRoleInfo(id);
  }

  /**
   * 獲取角色摘要資訊
   */
  @Get(':id/summary')
  @RequireRead('Role')
  @ApiOperation({
    summary: '獲取角色摘要資訊',
    description: '獲取角色的摘要資訊，適用於列表顯示',
  })
  @ApiParam({ name: 'id', description: '角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色摘要資訊',
    type: Object,
  })
  async getRoleSummary(@Param('id') id: string): Promise<RoleSummary> {
    return this.unifiedRoleService.getRoleSummary(id);
  }

  /**
   * 批量獲取角色摘要
   */
  @Post('batch-summaries')
  @RequireRead('Role')
  @ApiOperation({
    summary: '批量獲取角色摘要',
    description: '批量獲取多個角色的摘要資訊',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量角色摘要結果',
    type: Object,
  })
  async getBatchRoleSummaries(
    @Query('roleIds') roleIds: string
  ): Promise<BatchRoleSummaryResult> {
    const roleIdArray = roleIds.split(',').map(id => id.trim());
    return this.unifiedRoleService.getBatchRoleSummaries(roleIdArray);
  }

  /**
   * 角色健康檢查
   */
  @Get(':id/health-check')
  @RequireRead('Role')
  @ApiOperation({
    summary: '角色健康檢查',
    description: '檢查角色的完整性和一致性',
  })
  @ApiParam({ name: 'id', description: '角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色健康檢查結果',
    type: Object,
  })
  async performRoleHealthCheck(@Param('id') id: string): Promise<RoleHealthCheck> {
    return this.unifiedRoleService.performRoleHealthCheck(id);
  }

  /**
   * 角色使用分析
   */
  @Get(':id/usage-analysis')
  @RequireRead('Role')
  @ApiOperation({
    summary: '角色使用分析',
    description: '分析角色的使用情況和影響範圍',
  })
  @ApiParam({ name: 'id', description: '角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色使用分析結果',
    type: Object,
  })
  async analyzeRoleUsage(@Param('id') id: string): Promise<RoleUsageAnalysis> {
    return this.unifiedRoleService.analyzeRoleUsage(id);
  }

  /**
   * 批量健康檢查
   */
  @Post('batch-health-check')
  @RequireRead('Role')
  @ApiOperation({
    summary: '批量角色健康檢查',
    description: '對多個角色進行健康檢查',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量健康檢查結果',
    type: Object,
  })
  async batchHealthCheck(
    @Query('roleIds') roleIds: string
  ): Promise<{
    results: RoleHealthCheck[];
    summary: {
      total: number;
      healthy: number;
      unhealthy: number;
      commonIssues: string[];
      commonWarnings: string[];
    };
  }> {
    const roleIdArray = roleIds.split(',').map(id => id.trim());
    
    const results = await Promise.all(
      roleIdArray.map(roleId => 
        this.unifiedRoleService.performRoleHealthCheck(roleId)
      )
    );

    // 統計結果
    const healthy = results.filter(r => r.isHealthy).length;
    const unhealthy = results.length - healthy;

    // 收集常見問題
    const allIssues = results.flatMap(r => r.issues);
    const allWarnings = results.flatMap(r => r.warnings);
    
    const issueCount = allIssues.reduce((acc, issue) => {
      acc[issue] = (acc[issue] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const warningCount = allWarnings.reduce((acc, warning) => {
      acc[warning] = (acc[warning] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const commonIssues = Object.entries(issueCount)
      .filter(([, count]) => count > 1)
      .map(([issue]) => issue);

    const commonWarnings = Object.entries(warningCount)
      .filter(([, count]) => count > 1)
      .map(([warning]) => warning);

    return {
      results,
      summary: {
        total: results.length,
        healthy,
        unhealthy,
        commonIssues,
        commonWarnings,
      },
    };
  }

  /**
   * 角色系統概覽
   */
  @Get('system-overview')
  @RequireRead('Role')
  @ApiOperation({
    summary: '角色系統概覽',
    description: '獲取整個角色系統的概覽資訊',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色系統概覽',
    type: Object,
  })
  async getSystemOverview(): Promise<{
    totalRoles: number;
    rolesByScope: Record<string, number>;
    totalUsers: number;
    usersByType: Record<string, number>;
    hierarchyDepth: number;
    orphanedRoles: number;
    mostUsedRoles: Array<{ roleId: string; roleName: string; userCount: number }>;
    leastUsedRoles: Array<{ roleId: string; roleName: string; userCount: number }>;
    systemHealth: {
      overallHealth: 'healthy' | 'warning' | 'critical';
      issueCount: number;
      warningCount: number;
    };
  }> {
    // 這裡可以實作系統概覽邏輯
    // 由於複雜性，這裡提供一個基本的實作框架
    return {
      totalRoles: 0,
      rolesByScope: {},
      totalUsers: 0,
      usersByType: {},
      hierarchyDepth: 0,
      orphanedRoles: 0,
      mostUsedRoles: [],
      leastUsedRoles: [],
      systemHealth: {
        overallHealth: 'healthy',
        issueCount: 0,
        warningCount: 0,
      },
    };
  }

  /**
   * 角色建議
   */
  @Get('suggestions')
  @RequireRead('Role')
  @ApiOperation({
    summary: '獲取角色最佳化建議',
    description: '分析角色系統並提供最佳化建議',
  })
  @ApiQuery({ name: 'includeAutoApplicable', required: false, type: Boolean })
  @ApiQuery({ name: 'priority', required: false, enum: ['low', 'medium', 'high'] })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色最佳化建議',
    type: Object,
  })
  async getRoleSuggestions(
    @Query('includeAutoApplicable') includeAutoApplicable?: boolean,
    @Query('priority') priority?: 'low' | 'medium' | 'high'
  ) {
    // 這裡可以實作角色建議邏輯
    return {
      suggestions: [],
      summary: {
        totalSuggestions: 0,
        highPriority: 0,
        autoApplicable: 0,
        estimatedImpact: 'low' as const,
      },
      generatedAt: new Date(),
    };
  }

  /**
   * 角色比較
   */
  @Get('compare/:id1/:id2')
  @RequireRead('Role')
  @ApiOperation({
    summary: '比較兩個角色',
    description: '比較兩個角色的權限、用戶和層級差異',
  })
  @ApiParam({ name: 'id1', description: '第一個角色 ID' })
  @ApiParam({ name: 'id2', description: '第二個角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色比較結果',
    type: Object,
  })
  async compareRoles(
    @Param('id1') id1: string,
    @Param('id2') id2: string
  ) {
    // 這裡可以實作角色比較邏輯
    const [role1Summary, role2Summary] = await Promise.all([
      this.unifiedRoleService.getRoleSummary(id1),
      this.unifiedRoleService.getRoleSummary(id2),
    ]);

    return {
      role1: role1Summary,
      role2: role2Summary,
      differences: {
        permissions: {
          onlyInRole1: [],
          onlyInRole2: [],
          common: [],
        },
        users: {
          onlyInRole1: 0,
          onlyInRole2: 0,
          common: 0,
        },
        hierarchy: {
          levelDifference: Math.abs(role1Summary.hierarchyLevel - role2Summary.hierarchyLevel),
          differentParents: false,
          differentChildren: false,
        },
      },
      similarity: {
        permissionSimilarity: 0,
        userSimilarity: 0,
        overallSimilarity: 0,
      },
      recommendations: [],
    };
  }
}
