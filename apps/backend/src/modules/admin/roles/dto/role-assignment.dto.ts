import { IsString, <PERSON><PERSON>rray, Is<PERSON>num, IsOptional, IsUUID, ArrayNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserType } from '../types/role.types';

/**
 * 角色指派 DTO
 */
export class AssignRolesDto {
  @ApiProperty({
    description: '用戶 ID',
    example: 'user-123',
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: '用戶類型',
    enum: UserType,
    example: UserType.TENANT,
  })
  @IsEnum(UserType)
  userType: UserType;

  @ApiProperty({
    description: '角色 ID 陣列',
    type: [String],
    example: ['role-1', 'role-2'],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsUUID(undefined, { each: true })
  roleIds: string[];

  @ApiPropertyOptional({
    description: '租戶 ID（租戶用戶必填）',
    example: 'tenant-123',
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  tenantId?: string;

  @ApiPropertyOptional({
    description: '指派原因',
    example: '新員工入職',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * 角色移除 DTO
 */
export class RemoveRolesDto {
  @ApiProperty({
    description: '用戶 ID',
    example: 'user-123',
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: '用戶類型',
    enum: UserType,
    example: UserType.TENANT,
  })
  @IsEnum(UserType)
  userType: UserType;

  @ApiProperty({
    description: '要移除的角色 ID 陣列',
    type: [String],
    example: ['role-1', 'role-2'],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsUUID(undefined, { each: true })
  roleIds: string[];

  @ApiPropertyOptional({
    description: '移除原因',
    example: '職位調整',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * 角色替換 DTO
 */
export class ReplaceRolesDto {
  @ApiProperty({
    description: '用戶 ID',
    example: 'user-123',
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: '用戶類型',
    enum: UserType,
    example: UserType.TENANT,
  })
  @IsEnum(UserType)
  userType: UserType;

  @ApiProperty({
    description: '新的角色 ID 陣列',
    type: [String],
    example: ['role-3', 'role-4'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsUUID(undefined, { each: true })
  roleIds: string[];

  @ApiPropertyOptional({
    description: '租戶 ID（租戶用戶必填）',
    example: 'tenant-123',
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  tenantId?: string;

  @ApiPropertyOptional({
    description: '替換原因',
    example: '角色重組',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * 批量角色操作項目 DTO
 */
export class BatchRoleOperationItemDto {
  @ApiProperty({
    description: '用戶 ID',
    example: 'user-123',
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: '用戶類型',
    enum: UserType,
    example: UserType.TENANT,
  })
  @IsEnum(UserType)
  userType: UserType;

  @ApiProperty({
    description: '操作類型',
    enum: ['assign', 'remove', 'replace'],
    example: 'assign',
  })
  @IsEnum(['assign', 'remove', 'replace'])
  action: 'assign' | 'remove' | 'replace';

  @ApiProperty({
    description: '角色 ID 陣列',
    type: [String],
    example: ['role-1', 'role-2'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsUUID(undefined, { each: true })
  roleIds: string[];

  @ApiPropertyOptional({
    description: '租戶 ID（租戶用戶必填）',
    example: 'tenant-123',
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  tenantId?: string;
}

/**
 * 批量角色操作 DTO
 */
export class BatchRoleOperationDto {
  @ApiProperty({
    description: '批量操作項目',
    type: [BatchRoleOperationItemDto],
  })
  @IsArray()
  @ArrayNotEmpty()
  operations: BatchRoleOperationItemDto[];

  @ApiPropertyOptional({
    description: '操作原因',
    example: '組織架構調整',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * 設置父角色 DTO
 */
export class SetParentRoleDto {
  @ApiProperty({
    description: '角色 ID',
    example: 'role-123',
  })
  @IsString()
  @IsUUID()
  roleId: string;

  @ApiPropertyOptional({
    description: '父角色 ID（null 表示移除父角色）',
    example: 'parent-role-123',
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  parentRoleId?: string | null;
}

/**
 * 查詢用戶角色 DTO
 */
export class GetUserRoleDto {
  @ApiProperty({
    description: '用戶 ID',
    example: 'user-123',
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: '用戶類型',
    enum: UserType,
    example: UserType.TENANT,
  })
  @IsEnum(UserType)
  userType: UserType;
}

/**
 * 檢查用戶權限 DTO
 */
export class CheckUserPermissionDto {
  @ApiProperty({
    description: '用戶 ID',
    example: 'user-123',
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: '用戶類型',
    enum: UserType,
    example: UserType.TENANT,
  })
  @IsEnum(UserType)
  userType: UserType;

  @ApiProperty({
    description: '權限 ID',
    example: 'permission-123',
  })
  @IsString()
  @IsUUID()
  permissionId: string;
}

/**
 * 批量檢查用戶權限 DTO
 */
export class CheckUserPermissionsDto {
  @ApiProperty({
    description: '用戶 ID',
    example: 'user-123',
  })
  @IsString()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: '用戶類型',
    enum: UserType,
    example: UserType.TENANT,
  })
  @IsEnum(UserType)
  userType: UserType;

  @ApiProperty({
    description: '權限 ID 陣列',
    type: [String],
    example: ['permission-1', 'permission-2'],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsUUID(undefined, { each: true })
  permissionIds: string[];

  @ApiPropertyOptional({
    description: '檢查模式：any（任一權限）或 all（所有權限）',
    enum: ['any', 'all'],
    example: 'any',
    default: 'any',
  })
  @IsOptional()
  @IsEnum(['any', 'all'])
  mode?: 'any' | 'all' = 'any';
}
