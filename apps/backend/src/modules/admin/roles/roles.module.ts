import { Modu<PERSON> } from '@nestjs/common';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { CaslModule } from '../../../casl/casl.module';
import { RoleAssignmentService } from './services/role-assignment.service';
import { RoleHierarchyService } from './services/role-hierarchy.service';
import { UserRoleService } from './services/user-role.service';
import { UnifiedRoleService } from './services/unified-role.service';
import { RoleAssignmentController } from './controllers/role-assignment.controller';
import { UnifiedRoleController } from './controllers/unified-role.controller';

@Module({
  imports: [PrismaModule, CaslModule],
  controllers: [
    RolesController,
    RoleAssignmentController,
    UnifiedRoleController
  ],
  providers: [
    RolesService,
    RoleAssignmentService,
    RoleHierarchyService,
    UserRoleService,
    UnifiedRoleService,
  ],
  exports: [
    RolesService,
    RoleAssignmentService,
    RoleHierarchyService,
    UserRoleService,
    UnifiedRoleService,
  ]
})
export class RolesModule {}