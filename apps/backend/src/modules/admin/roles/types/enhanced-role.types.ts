import { RoleScope } from '@prisma/client';
import { UserType, UserRoleInfo, RoleHierarchyInfo } from '../interfaces/role-assignment.interface';

/**
 * 增強的角色資訊介面
 */
export interface EnhancedRoleInfo {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  scope: RoleScope;
  isSystem: boolean;
  tenantId?: string;
  parentRoleId?: string;
  createdAt: Date;
  updatedAt: Date;
  permissions: string[];
  hierarchy?: RoleHierarchyInfo;
  effectivePermissions?: string[];
}

/**
 * 角色摘要資訊介面
 */
export interface RoleSummary {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  scope: RoleScope;
  isSystem: boolean;
  userCount: number;
  permissionCount: number;
  hierarchyLevel: number;
  hasChildren: boolean;
  hasParent: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 完整角色資訊介面
 */
export interface CompleteRoleInfo {
  role: EnhancedRoleInfo;
  hierarchy: RoleHierarchyInfo;
  users: {
    total: UserRoleInfo[];
    systemUsers: UserRoleInfo[];
    tenantUsers: UserRoleInfo[];
    byTenant: Array<{
      tenantId: string;
      tenantName: string;
      users: UserRoleInfo[];
    }>;
  };
  permissions: {
    direct: string[];
    inherited: string[];
    effective: string[];
  };
  statistics: RoleStatistics;
  metadata: {
    lastUpdated: Date;
    dataVersion: string;
  };
}

/**
 * 角色統計資訊介面
 */
export interface RoleStatistics {
  userCount: number;
  systemUsers: number;
  tenantUsers: number;
  directPermissions: number;
  inheritedPermissions: number;
  effectivePermissions: number;
  hierarchyLevel: number;
  childRolesCount: number;
  hasParentRole: boolean;
}

/**
 * 角色健康檢查結果介面
 */
export interface RoleHealthCheck {
  roleId: string;
  isHealthy: boolean;
  issues: string[];
  warnings: string[];
  recommendations: string[];
  checkedAt: Date;
}

/**
 * 角色使用分析結果介面
 */
export interface RoleUsageAnalysis {
  roleId: string;
  roleName: string;
  impactScope: {
    directUsers: number;
    indirectUsers: number;
    affectedRoles: number;
    permissionScope: number;
  };
  usagePatterns: {
    isActivelyUsed: boolean;
    isHierarchyRoot: boolean;
    isHierarchyLeaf: boolean;
    isIsolated: boolean;
    hasSystemUsers: boolean;
    hasTenantUsers: boolean;
    isMultiTenant: boolean;
  };
  riskAssessment: {
    deletionRisk: 'low' | 'medium' | 'high';
    modificationRisk: 'low' | 'medium' | 'high';
    securityRisk: 'low' | 'medium' | 'high';
  };
  hierarchy: {
    level: number;
    parentRoles: string[];
    childRoles: string[];
  };
  recommendations: string[];
  analyzedAt: Date;
}

/**
 * 批量角色摘要結果介面
 */
export interface BatchRoleSummaryResult {
  successful: RoleSummary[];
  failed: Array<{
    roleId: string;
    error: string;
  }>;
  totalRequested: number;
  successCount: number;
  failureCount: number;
}

/**
 * 角色用戶統計詳情介面
 */
export interface RoleUserStatistics {
  totalUsers: number;
  systemUsers: number;
  tenantUsers: number;
  usersByTenant?: Record<string, number>;
}

/**
 * 角色操作結果介面
 */
export interface RoleOperationResult {
  success: boolean;
  roleId: string;
  operation: 'create' | 'update' | 'delete' | 'assign' | 'remove';
  message?: string;
  error?: string;
  timestamp: Date;
}

/**
 * 角色驗證結果介面
 */
export interface RoleValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  warnings: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

/**
 * 角色搜索條件介面
 */
export interface RoleSearchCriteria {
  name?: string;
  displayName?: string;
  scope?: RoleScope;
  isSystem?: boolean;
  tenantId?: string;
  hasUsers?: boolean;
  hasPermissions?: boolean;
  hierarchyLevel?: number;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * 角色搜索結果介面
 */
export interface RoleSearchResult {
  roles: RoleSummary[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * 角色比較結果介面
 */
export interface RoleComparisonResult {
  role1: RoleSummary;
  role2: RoleSummary;
  differences: {
    permissions: {
      onlyInRole1: string[];
      onlyInRole2: string[];
      common: string[];
    };
    users: {
      onlyInRole1: number;
      onlyInRole2: number;
      common: number;
    };
    hierarchy: {
      levelDifference: number;
      differentParents: boolean;
      differentChildren: boolean;
    };
  };
  similarity: {
    permissionSimilarity: number; // 0-1
    userSimilarity: number; // 0-1
    overallSimilarity: number; // 0-1
  };
  recommendations: string[];
}

/**
 * 角色審計記錄介面
 */
export interface RoleAuditRecord {
  id: string;
  roleId: string;
  roleName: string;
  operation: 'create' | 'update' | 'delete' | 'assign' | 'remove' | 'permission_change';
  operatorId: string;
  operatorType: UserType;
  changes: Record<string, {
    before: any;
    after: any;
  }>;
  reason?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * 角色權限矩陣介面
 */
export interface RolePermissionMatrix {
  roles: Array<{
    id: string;
    name: string;
    displayName: string;
  }>;
  permissions: Array<{
    id: string;
    action: string;
    subject: string;
    category: string;
  }>;
  matrix: Record<string, Record<string, boolean>>; // roleId -> permissionId -> hasPermission
  effectiveMatrix: Record<string, Record<string, boolean>>; // 包含繼承權限
}

/**
 * 角色建議介面
 */
export interface RoleSuggestion {
  type: 'create' | 'merge' | 'split' | 'delete' | 'restructure';
  title: string;
  description: string;
  affectedRoles: string[];
  expectedBenefits: string[];
  potentialRisks: string[];
  priority: 'low' | 'medium' | 'high';
  estimatedEffort: 'low' | 'medium' | 'high';
  autoApplicable: boolean;
}

/**
 * 角色最佳化建議介面
 */
export interface RoleOptimizationSuggestions {
  suggestions: RoleSuggestion[];
  summary: {
    totalSuggestions: number;
    highPriority: number;
    autoApplicable: number;
    estimatedImpact: 'low' | 'medium' | 'high';
  };
  generatedAt: Date;
}

/**
 * 角色匯出配置介面
 */
export interface RoleExportConfig {
  includeUsers: boolean;
  includePermissions: boolean;
  includeHierarchy: boolean;
  includeStatistics: boolean;
  format: 'json' | 'csv' | 'xlsx';
  scope?: RoleScope;
  tenantId?: string;
}

/**
 * 角色匯入配置介面
 */
export interface RoleImportConfig {
  overwriteExisting: boolean;
  validateHierarchy: boolean;
  validatePermissions: boolean;
  dryRun: boolean;
  defaultScope: RoleScope;
  defaultTenantId?: string;
}

/**
 * 角色匯入結果介面
 */
export interface RoleImportResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  skipped: number;
  errors: Array<{
    row: number;
    roleId?: string;
    error: string;
  }>;
  warnings: Array<{
    row: number;
    roleId?: string;
    warning: string;
  }>;
  summary: {
    rolesCreated: number;
    rolesUpdated: number;
    permissionsAssigned: number;
    hierarchyRelationsCreated: number;
  };
}
