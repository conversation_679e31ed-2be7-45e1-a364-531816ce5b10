import { RoleScope } from '@prisma/client';

/**
 * 用戶類型枚舉
 */
export enum UserType {
  SYSTEM = 'system',
  TENANT = 'tenant',
}

/**
 * 角色指派請求介面
 */
export interface RoleAssignmentRequest {
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 角色 ID 陣列
   */
  roleIds: string[];
  
  /**
   * 租戶 ID（租戶用戶必填）
   */
  tenantId?: string;
  
  /**
   * 指派原因
   */
  reason?: string;
  
  /**
   * 指派者 ID
   */
  assignedBy?: string;
}

/**
 * 角色移除請求介面
 */
export interface RoleRemovalRequest {
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 要移除的角色 ID 陣列
   */
  roleIds: string[];
  
  /**
   * 移除原因
   */
  reason?: string;
  
  /**
   * 移除者 ID
   */
  removedBy?: string;
}

/**
 * 批量角色操作請求介面
 */
export interface BatchRoleOperationRequest {
  /**
   * 用戶操作陣列
   */
  operations: Array<{
    userId: string;
    userType: UserType;
    action: 'assign' | 'remove' | 'replace';
    roleIds: string[];
    tenantId?: string;
  }>;
  
  /**
   * 操作原因
   */
  reason?: string;
  
  /**
   * 操作者 ID
   */
  operatedBy?: string;
}

/**
 * 角色指派結果介面
 */
export interface RoleAssignmentResult {
  /**
   * 是否成功
   */
  success: boolean;
  
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 指派的角色 ID 陣列
   */
  assignedRoles: string[];
  
  /**
   * 移除的角色 ID 陣列
   */
  removedRoles: string[];
  
  /**
   * 錯誤訊息（如果有）
   */
  error?: string;
  
  /**
   * 操作時間戳
   */
  timestamp: Date;
}

/**
 * 批量角色操作結果介面
 */
export interface BatchRoleOperationResult {
  /**
   * 總操作數
   */
  totalOperations: number;
  
  /**
   * 成功操作數
   */
  successCount: number;
  
  /**
   * 失敗操作數
   */
  failureCount: number;
  
  /**
   * 個別操作結果
   */
  results: RoleAssignmentResult[];
  
  /**
   * 整體錯誤訊息（如果有）
   */
  error?: string;
  
  /**
   * 操作時間戳
   */
  timestamp: Date;
}

/**
 * 用戶角色資訊介面
 */
export interface UserRoleInfo {
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 用戶基本資訊
   */
  user: {
    id: string;
    email: string;
    name?: string;
    status: string;
  };
  
  /**
   * 角色陣列
   */
  roles: Array<{
    id: string;
    name: string;
    displayName: string;
    description?: string;
    scope: RoleScope;
    isSystem: boolean;
    assignedAt: Date;
  }>;
  
  /**
   * 租戶資訊（租戶用戶）
   */
  tenant?: {
    id: string;
    name: string;
  };
}

/**
 * 角色衝突檢查結果介面
 */
export interface RoleConflictCheck {
  /**
   * 是否有衝突
   */
  hasConflict: boolean;
  
  /**
   * 衝突詳情
   */
  conflicts: Array<{
    type: 'scope_mismatch' | 'hierarchy_violation' | 'permission_conflict' | 'tenant_mismatch';
    message: string;
    conflictingRoles: string[];
  }>;
  
  /**
   * 建議
   */
  suggestions: string[];
}

/**
 * 角色層級資訊介面
 */
export interface RoleHierarchyInfo {
  /**
   * 角色 ID
   */
  roleId: string;
  
  /**
   * 角色名稱
   */
  roleName: string;
  
  /**
   * 父角色 ID
   */
  parentRoleId?: string;
  
  /**
   * 子角色 ID 陣列
   */
  childRoleIds: string[];
  
  /**
   * 層級深度
   */
  level: number;
  
  /**
   * 繼承的權限
   */
  inheritedPermissions: string[];
  
  /**
   * 直接權限
   */
  directPermissions: string[];
}

/**
 * 角色變更歷史介面
 */
export interface RoleChangeHistory {
  /**
   * 變更 ID
   */
  id: string;
  
  /**
   * 用戶 ID
   */
  userId: string;
  
  /**
   * 用戶類型
   */
  userType: UserType;
  
  /**
   * 變更類型
   */
  changeType: 'assign' | 'remove' | 'replace';
  
  /**
   * 變更前的角色
   */
  previousRoles: string[];
  
  /**
   * 變更後的角色
   */
  newRoles: string[];
  
  /**
   * 變更原因
   */
  reason?: string;
  
  /**
   * 變更者 ID
   */
  changedBy: string;
  
  /**
   * 變更時間
   */
  changedAt: Date;
  
  /**
   * 租戶 ID（如果適用）
   */
  tenantId?: string;
}

/**
 * 增強的角色資訊介面
 */
export interface EnhancedRoleInfo {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  scope: RoleScope;
  isSystem: boolean;
  tenantId?: string;
  parentRoleId?: string;
  createdAt: Date;
  updatedAt: Date;
  permissions: string[];
  hierarchy?: RoleHierarchyInfo;
  effectivePermissions?: string[];
}

/**
 * 角色摘要資訊介面
 */
export interface RoleSummary {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  scope: RoleScope;
  isSystem: boolean;
  userCount: number;
  permissionCount: number;
  hierarchyLevel: number;
  hasChildren: boolean;
  hasParent: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 角色統計資訊介面
 */
export interface RoleStatistics {
  userCount: number;
  systemUsers: number;
  tenantUsers: number;
  directPermissions: number;
  inheritedPermissions: number;
  effectivePermissions: number;
  hierarchyLevel: number;
  childRolesCount: number;
  hasParentRole: boolean;
}

/**
 * 完整角色資訊介面
 */
export interface CompleteRoleInfo {
  role: EnhancedRoleInfo;
  hierarchy: RoleHierarchyInfo;
  users: {
    total: UserRoleInfo[];
    systemUsers: UserRoleInfo[];
    tenantUsers: UserRoleInfo[];
    byTenant: Array<{
      tenantId: string;
      tenantName: string;
      users: UserRoleInfo[];
    }>;
  };
  permissions: {
    direct: string[];
    inherited: string[];
    effective: string[];
  };
  statistics: RoleStatistics;
  metadata: {
    lastUpdated: Date;
    dataVersion: string;
  };
}

/**
 * 角色健康檢查結果介面
 */
export interface RoleHealthCheck {
  roleId: string;
  isHealthy: boolean;
  issues: string[];
  warnings: string[];
  recommendations: string[];
  checkedAt: Date;
}

/**
 * 角色使用分析結果介面
 */
export interface RoleUsageAnalysis {
  roleId: string;
  roleName: string;
  impactScope: {
    directUsers: number;
    indirectUsers: number;
    affectedRoles: number;
    permissionScope: number;
  };
  usagePatterns: {
    isActivelyUsed: boolean;
    isHierarchyRoot: boolean;
    isHierarchyLeaf: boolean;
    isIsolated: boolean;
    hasSystemUsers: boolean;
    hasTenantUsers: boolean;
    isMultiTenant: boolean;
  };
  riskAssessment: {
    deletionRisk: 'low' | 'medium' | 'high';
    modificationRisk: 'low' | 'medium' | 'high';
    securityRisk: 'low' | 'medium' | 'high';
  };
  hierarchy: {
    level: number;
    parentRoles: string[];
    childRoles: string[];
  };
  recommendations: string[];
  analyzedAt: Date;
}

/**
 * 批量角色摘要結果介面
 */
export interface BatchRoleSummaryResult {
  successful: RoleSummary[];
  failed: Array<{
    roleId: string;
    error: string;
  }>;
  totalRequested: number;
  successCount: number;
  failureCount: number;
}

/**
 * 角色用戶統計詳情介面
 */
export interface RoleUserStatistics {
  totalUsers: number;
  systemUsers: number;
  tenantUsers: number;
  usersByTenant?: Record<string, number>;
}

/**
 * 角色操作結果介面
 */
export interface RoleOperationResult {
  success: boolean;
  roleId: string;
  operation: 'create' | 'update' | 'delete' | 'assign' | 'remove';
  message?: string;
  error?: string;
  timestamp: Date;
}

/**
 * 角色驗證結果介面
 */
export interface RoleValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  warnings: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

/**
 * 角色搜索條件介面
 */
export interface RoleSearchCriteria {
  name?: string;
  displayName?: string;
  scope?: RoleScope;
  isSystem?: boolean;
  tenantId?: string;
  hasUsers?: boolean;
  hasPermissions?: boolean;
  hierarchyLevel?: number;
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * 角色搜索結果介面
 */
export interface RoleSearchResult {
  roles: RoleSummary[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * 角色比較結果介面
 */
export interface RoleComparisonResult {
  role1: RoleSummary;
  role2: RoleSummary;
  differences: {
    permissions: {
      onlyInRole1: string[];
      onlyInRole2: string[];
      common: string[];
    };
    users: {
      onlyInRole1: number;
      onlyInRole2: number;
      common: number;
    };
    hierarchy: {
      levelDifference: number;
      differentParents: boolean;
      differentChildren: boolean;
    };
  };
  similarity: {
    permissionSimilarity: number; // 0-1
    userSimilarity: number; // 0-1
    overallSimilarity: number; // 0-1
  };
  recommendations: string[];
}

/**
 * 角色審計記錄介面
 */
export interface RoleAuditRecord {
  id: string;
  roleId: string;
  roleName: string;
  operation: 'create' | 'update' | 'delete' | 'assign' | 'remove' | 'permission_change';
  operatorId: string;
  operatorType: UserType;
  changes: Record<
    string,
    {
      before: any;
      after: any;
    }
  >;
  reason?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * 角色權限矩陣介面
 */
export interface RolePermissionMatrix {
  roles: Array<{
    id: string;
    name: string;
    displayName: string;
  }>;
  permissions: Array<{
    id: string;
    action: string;
    subject: string;
    category: string;
  }>;
  matrix: Record<string, Record<string, boolean>>; // roleId -> permissionId -> hasPermission
  effectiveMatrix: Record<string, Record<string, boolean>>; // 包含繼承權限
}

/**
 * 角色建議介面
 */
export interface RoleSuggestion {
  type: 'create' | 'merge' | 'split' | 'delete' | 'restructure';
  title: string;
  description: string;
  affectedRoles: string[];
  expectedBenefits: string[];
  potentialRisks: string[];
  priority: 'low' | 'medium' | 'high';
  estimatedEffort: 'low' | 'medium' | 'high';
  autoApplicable: boolean;
}

/**
 * 角色最佳化建議介面
 */
export interface RoleOptimizationSuggestions {
  suggestions: RoleSuggestion[];
  summary: {
    totalSuggestions: number;
    highPriority: number;
    autoApplicable: number;
    estimatedImpact: 'low' | 'medium' | 'high';
  };
  generatedAt: Date;
}

/**
 * 角色匯出配置介面
 */
export interface RoleExportConfig {
  includeUsers: boolean;
  includePermissions: boolean;
  includeHierarchy: boolean;
  includeStatistics: boolean;
  format: 'json' | 'csv' | 'xlsx';
  scope?: RoleScope;
  tenantId?: string;
}

/**
 * 角色匯入配置介面
 */
export interface RoleImportConfig {
  overwriteExisting: boolean;
  validateHierarchy: boolean;
  validatePermissions: boolean;
  dryRun: boolean;
  defaultScope: RoleScope;
  defaultTenantId?: string;
}

/**
 * 角色匯入結果介面
 */
export interface RoleImportResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  skipped: number;
  errors: Array<{
    row: number;
    roleId?: string;
    error: string;
  }>;
  warnings: Array<{
    row: number;
    roleId?: string;
    warning: string;
  }>;
  summary: {
    rolesCreated: number;
    rolesUpdated: number;
    permissionsAssigned: number;
    hierarchyRelationsCreated: number;
  };
}
