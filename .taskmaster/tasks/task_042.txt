# Task ID: 42
# Title: Refactor Admin Roles Module for Code Optimization and Maintainability
# Status: pending
# Dependencies: 3, 4
# Priority: high
# Description: Refactor the `apps/backend/src/modules/admin/roles/` directory to improve code structure by reducing file count, eliminating duplication, and simplifying dependencies, while ensuring all existing functionality and backward compatibility are maintained.
# Details:
The refactoring process for the `apps/backend/src/modules/admin/roles/` directory will be executed in three main phases:

**Phase 1: File Cleanup and Consolidation**
1.  **Scope Identification:** Target all files within the `apps/backend/src/modules/admin/roles/` directory.
2.  **Remove Redundant Documentation:** Identify and eliminate outdated comments, resolved TODOs, and internal notes that are no longer relevant.
3.  **Unify Type Definitions:** Consolidate scattered TypeScript `interface`, `type`, and `enum` definitions into a minimal number of logically grouped files (e.g., `roles.types.ts` or co-locate with primary consumers like DTOs or entities). Aim for clarity and avoid overly generic type files.
4.  **Update Import Paths:** Systematically review and update all `import` statements within the `roles` module and any external modules that import from `roles` to reflect the new file structure. Utilize path aliases (e.g., `@admin/roles`) if configured.
5.  **Optimize File Structure:** Aim for a flatter and more intuitive file organization. Group files by feature or responsibility (e.g., `services/`, `controllers/`, `dtos/`) if it enhances clarity, or consolidate further if the current structure is too granular. Ensure consistent naming conventions.

**Phase 2: Architecture Optimization**
1.  **Controller Consolidation:** Analyze existing controllers. If multiple controllers manage different aspects of the `roles` resource (e.g., separate controllers for read and write operations), consider merging them into a single, cohesive `RolesController`, ensuring adherence to REST principles and appropriate use of HTTP methods.
2.  **DTO Duplication Check & Refinement:** Review all Data Transfer Objects (DTOs). Identify common fields or structures. Create base DTOs and use inheritance (`extends`) or composition to reduce repetition (e.g., for `CreateRoleDto` and `UpdateRoleDto`).
3.  **Service Layer Refinement:** Examine service classes for duplicated logic or overly complex methods. Extract common operations into private helper methods, shared utility services, or consider base service classes if applicable. Focus on the Single Responsibility Principle.
4.  **Dependency Simplification:** Review constructor injections in services and controllers. If a class has an excessive number of dependencies, assess if its responsibilities can be narrowed or if dependencies can be aggregated or accessed via a more centralized mechanism.

**Phase 3: Final Validation and Cleanup**
1.  **Dependency Verification:** After refactoring, ensure that the module's dependencies (e.g., in NestJS module imports) accurately reflect its actual needs and that no unnecessary dependencies remain.
2.  **Functional Integrity Testing:** Manually test all role management functionalities through the application or an API client to confirm no regressions were introduced, complementing automated tests.
3.  **Code Pruning:** Utilize tools or manual inspection to identify and remove any unused variables, functions, classes, or import statements left over from the refactoring process.
4.  **Linting and Formatting:** Enforce code style consistency by running project-configured linters (e.g., ESLint) and formatters (e.g., Prettier).
5.  **Documentation Update:** Update any relevant README files, Swagger/OpenAPI documentation (e.g., `@nestjs/swagger` decorators), or other developer documentation to reflect changes in module structure or API, if any (though backward compatibility is a primary goal).

# Test Strategy:
A multi-layered testing approach will be used to ensure the refactoring is successful and maintains quality:

1.  **Static Analysis:**
    *   Run ESLint and Prettier (or project-specific tools) to catch syntax errors, style inconsistencies, and potential bugs early.

2.  **Unit Testing:**
    *   Ensure all existing unit tests for the `roles` module pass after refactoring.
    *   Update unit tests to reflect changes in file structure, class names, or method signatures.
    *   Write new unit tests for any newly created helper functions, consolidated logic, or previously untested code paths. Aim for high test coverage for all refactored components (services, controllers).

3.  **Integration Testing:**
    *   Verify the correct interaction between refactored `RolesController` and `RolesService`.
    *   Test interactions with the database layer for role persistence and retrieval.
    *   Ensure seamless integration with other modules, particularly `AuthModule` for permission checks if roles are used in authorization logic.

4.  **API (End-to-End) Testing:**
    *   Develop or update an automated E2E test suite (e.g., using Supertest for NestJS, or Postman collections for manual/semi-automated testing).
    *   Cover all API endpoints related to roles management:
        *   CRUD operations: Create, Read (list and by ID), Update, Delete roles.
        *   Endpoints for assigning/revoking permissions to/from roles.
    *   Verify HTTP status codes, response body structures, and data accuracy.
    *   Test with various valid and invalid inputs, and different user contexts (e.g., admin vs. non-admin access).

5.  **Backward Compatibility Verification:**
    *   Strictly ensure that the public API contract (endpoint paths, request/response DTO schemas) remains unchanged to avoid breaking existing clients.
    *   If any unavoidable, non-breaking changes are made (e.g., adding a new optional field to a response), they must be clearly documented and communicated.

6.  **Code Review:**
    *   Conduct a thorough peer review of all changes. The review will focus on:
        *   Achievement of refactoring goals: reduced file count, eliminated code duplication, simplified dependencies.
        *   Clarity, maintainability, and readability of the refactored code.
        *   Adherence to NestJS best practices and project-specific coding standards.
        *   Completeness and effectiveness of the accompanying tests.

# Subtasks:
## 1. 移除冗餘文檔檔案 [done]
### Dependencies: None
### Description: 刪除 INTEGRATION_GUIDE.md, INTEGRATION_COMPLETE.md, examples/role-management.example.ts, integration.spec.ts
### Details:


## 2. 型別定義統一整合 [done]
### Dependencies: None
### Description: 合併 interfaces/role-assignment.interface.ts 和 types/enhanced-role.types.ts 為單一檔案 types/role.types.ts
### Details:


## 3. 匯入路徑批量更新 [done]
### Dependencies: None
### Description: 更新所有檔案中的型別匯入路徑，指向新的統一型別檔案 types/role.types.ts
### Details:


## 4. 編譯驗證與錯誤修正 [done]
### Dependencies: None
### Description: 檢查編譯錯誤並修正關鍵問題，確保 TypeScript 編譯無誤
### Details:


