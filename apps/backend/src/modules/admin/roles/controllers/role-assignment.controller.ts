import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';
import { RoleAssignmentService } from '../services/role-assignment.service';
import { RoleHierarchyService } from '../services/role-hierarchy.service';
import { UserRoleService } from '../services/user-role.service';
import {
  AssignRolesDto,
  RemoveRolesDto,
  ReplaceRolesDto,
  BatchRoleOperationDto,
  SetParentRoleDto,
  GetUserRoleDto,
  CheckUserPermissionDto,
  CheckUserPermissionsDto,
} from '../dto/role-assignment.dto';
import {
  UserType,
  RoleAssignmentResult,
  BatchRoleOperationResult,
  UserRoleInfo,
  RoleHierarchyInfo,
} from '../types/role.types';
import { JwtUser } from '../../../../types/jwt-user.type';
import { UnifiedPermissionGuard, RequireManage, RequireRead } from '../../../../casl';
import { RoleScope } from '@prisma/client';

/**
 * 角色指派管理控制器
 * 提供角色指派、移除、查詢等功能
 */
@ApiTags('admin/role-assignment')
@ApiBearerAuth()
@Controller('admin/role-assignment')
@UseGuards(UnifiedPermissionGuard)
export class RoleAssignmentController {
  constructor(
    private readonly roleAssignmentService: RoleAssignmentService,
    private readonly roleHierarchyService: RoleHierarchyService,
    private readonly userRoleService: UserRoleService
  ) {}

  /**
   * 指派角色給用戶
   */
  @Post('assign')
  @RequireManage('Role')
  @ApiOperation({ summary: '指派角色給用戶' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色指派成功',
    type: Object,
  })
  async assignRoles(
    @Body() assignRolesDto: AssignRolesDto,
    @Req() req: Request
  ): Promise<RoleAssignmentResult> {
    const user = req.user as JwtUser;
    
    return this.roleAssignmentService.assignRoles({
      ...assignRolesDto,
      assignedBy: user.id,
    });
  }

  /**
   * 移除用戶角色
   */
  @Post('remove')
  @RequireManage('Role')
  @ApiOperation({ summary: '移除用戶角色' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色移除成功',
    type: Object,
  })
  async removeRoles(
    @Body() removeRolesDto: RemoveRolesDto,
    @Req() req: Request
  ): Promise<RoleAssignmentResult> {
    const user = req.user as JwtUser;
    
    return this.roleAssignmentService.removeRoles({
      ...removeRolesDto,
      removedBy: user.id,
    });
  }

  /**
   * 替換用戶角色
   */
  @Put('replace')
  @RequireManage('Role')
  @ApiOperation({ summary: '替換用戶角色' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色替換成功',
    type: Object,
  })
  async replaceRoles(
    @Body() replaceRolesDto: ReplaceRolesDto,
    @Req() req: Request
  ): Promise<RoleAssignmentResult> {
    const user = req.user as JwtUser;
    
    return this.roleAssignmentService.replaceRoles({
      ...replaceRolesDto,
      assignedBy: user.id,
    });
  }

  /**
   * 批量角色操作
   */
  @Post('batch')
  @RequireManage('Role')
  @ApiOperation({ summary: '批量角色操作' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量操作完成',
    type: Object,
  })
  async batchRoleOperations(
    @Body() batchOperationDto: BatchRoleOperationDto,
    @Req() req: Request
  ): Promise<BatchRoleOperationResult> {
    const user = req.user as JwtUser;
    
    return this.roleAssignmentService.batchRoleOperations({
      ...batchOperationDto,
      operatedBy: user.id,
    });
  }

  /**
   * 獲取用戶角色資訊
   */
  @Get('user/:userId/:userType')
  @RequireRead('Role')
  @ApiOperation({ summary: '獲取用戶角色資訊' })
  @ApiParam({ name: 'userId', description: '用戶 ID' })
  @ApiParam({ name: 'userType', enum: UserType, description: '用戶類型' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶角色資訊',
    type: Object,
  })
  async getUserRoleInfo(
    @Param('userId') userId: string,
    @Param('userType') userType: UserType
  ): Promise<UserRoleInfo> {
    return this.roleAssignmentService.getUserRoleInfo(userId, userType);
  }

  /**
   * 獲取用戶角色層級資訊
   */
  @Get('user/:userId/:userType/hierarchy')
  @RequireRead('Role')
  @ApiOperation({ summary: '獲取用戶角色層級資訊' })
  @ApiParam({ name: 'userId', description: '用戶 ID' })
  @ApiParam({ name: 'userType', enum: UserType, description: '用戶類型' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶角色層級資訊',
    type: Object,
  })
  async getUserRoleHierarchy(
    @Param('userId') userId: string,
    @Param('userType') userType: UserType
  ) {
    return this.userRoleService.getUserRoleHierarchy(userId, userType);
  }

  /**
   * 獲取用戶有效權限
   */
  @Get('user/:userId/:userType/permissions')
  @RequireRead('Role')
  @ApiOperation({ summary: '獲取用戶有效權限' })
  @ApiParam({ name: 'userId', description: '用戶 ID' })
  @ApiParam({ name: 'userType', enum: UserType, description: '用戶類型' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶有效權限列表',
    type: [String],
  })
  async getUserEffectivePermissions(
    @Param('userId') userId: string,
    @Param('userType') userType: UserType
  ): Promise<string[]> {
    return this.userRoleService.getUserEffectivePermissions(userId, userType);
  }

  /**
   * 檢查用戶權限
   */
  @Post('check-permission')
  @RequireRead('Role')
  @ApiOperation({ summary: '檢查用戶是否有特定權限' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '權限檢查結果',
    type: Boolean,
  })
  async checkUserPermission(
    @Body() checkPermissionDto: CheckUserPermissionDto
  ): Promise<{ hasPermission: boolean }> {
    const hasPermission = await this.userRoleService.userHasPermission(
      checkPermissionDto.userId,
      checkPermissionDto.userType,
      checkPermissionDto.permissionId
    );

    return { hasPermission };
  }

  /**
   * 批量檢查用戶權限
   */
  @Post('check-permissions')
  @RequireRead('Role')
  @ApiOperation({ summary: '批量檢查用戶權限' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量權限檢查結果',
    type: Object,
  })
  async checkUserPermissions(
    @Body() checkPermissionsDto: CheckUserPermissionsDto
  ): Promise<{
    mode: string;
    hasPermissions: boolean;
    permissionResults: Array<{ permissionId: string; hasPermission: boolean }>;
  }> {
    const { userId, userType, permissionIds, mode = 'any' } = checkPermissionsDto;

    const effectivePermissions = await this.userRoleService.getUserEffectivePermissions(
      userId,
      userType
    );

    const permissionResults = permissionIds.map((permissionId) => ({
      permissionId,
      hasPermission: effectivePermissions.includes(permissionId),
    }));

    const hasPermissions = mode === 'any'
      ? permissionResults.some((result) => result.hasPermission)
      : permissionResults.every((result) => result.hasPermission);

    return {
      mode,
      hasPermissions,
      permissionResults,
    };
  }

  /**
   * 根據範圍獲取用戶列表
   */
  @Get('users/by-scope/:scope')
  @RequireRead('Role')
  @ApiOperation({ summary: '根據範圍獲取用戶列表' })
  @ApiParam({ name: 'scope', enum: RoleScope, description: '角色範圍' })
  @ApiQuery({ name: 'tenantId', required: false, description: '租戶 ID（非系統範圍時可選）' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶列表',
    type: [Object],
  })
  async getUsersByScope(
    @Param('scope') scope: RoleScope,
    @Query('tenantId') tenantId?: string
  ): Promise<UserRoleInfo[]> {
    return this.userRoleService.getUsersByScope(scope, tenantId);
  }

  /**
   * 獲取具有特定角色的用戶
   */
  @Get('users/by-role/:roleId')
  @RequireRead('Role')
  @ApiOperation({ summary: '獲取具有特定角色的用戶' })
  @ApiParam({ name: 'roleId', description: '角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶列表',
    type: [Object],
  })
  async getUsersByRole(@Param('roleId') roleId: string): Promise<UserRoleInfo[]> {
    return this.userRoleService.getUsersByRole(roleId);
  }

  /**
   * 獲取具有特定權限的用戶
   */
  @Get('users/by-permission/:permissionId')
  @RequireRead('Role')
  @ApiOperation({ summary: '獲取具有特定權限的用戶' })
  @ApiParam({ name: 'permissionId', description: '權限 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '用戶列表',
    type: [Object],
  })
  async getUsersByPermission(@Param('permissionId') permissionId: string): Promise<UserRoleInfo[]> {
    return this.userRoleService.getUsersByPermission(permissionId);
  }

  /**
   * 設置角色的父角色
   */
  @Put('hierarchy/parent')
  @RequireManage('Role')
  @ApiOperation({ summary: '設置角色的父角色' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '父角色設置成功',
  })
  async setParentRole(@Body() setParentRoleDto: SetParentRoleDto): Promise<{ message: string }> {
    await this.roleHierarchyService.setParentRole(
      setParentRoleDto.roleId,
      setParentRoleDto.parentRoleId
    );

    return { message: '父角色設置成功' };
  }

  /**
   * 獲取角色層級資訊
   */
  @Get('hierarchy/:roleId')
  @RequireRead('Role')
  @ApiOperation({ summary: '獲取角色層級資訊' })
  @ApiParam({ name: 'roleId', description: '角色 ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '角色層級資訊',
    type: Object,
  })
  async getRoleHierarchy(@Param('roleId') roleId: string): Promise<RoleHierarchyInfo> {
    return this.roleHierarchyService.getRoleHierarchy(roleId);
  }

  /**
   * 驗證角色層級結構
   */
  @Get('hierarchy/validate')
  @RequireRead('Role')
  @ApiOperation({ summary: '驗證角色層級結構' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '層級結構驗證結果',
    type: Object,
  })
  async validateHierarchy() {
    return this.roleHierarchyService.validateHierarchy();
  }

  /**
   * 同步用戶權限快取
   */
  @Post('sync-cache/:userId/:userType')
  @RequireManage('Role')
  @ApiOperation({ summary: '同步用戶權限快取' })
  @ApiParam({ name: 'userId', description: '用戶 ID' })
  @ApiParam({ name: 'userType', enum: UserType, description: '用戶類型' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '權限快取同步成功',
  })
  async syncUserPermissionCache(
    @Param('userId') userId: string,
    @Param('userType') userType: UserType
  ): Promise<{ message: string }> {
    await this.userRoleService.syncUserPermissionCache(userId, userType);
    return { message: '權限快取同步成功' };
  }
}
